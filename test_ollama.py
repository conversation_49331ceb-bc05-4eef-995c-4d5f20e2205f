#!/usr/bin/env python
"""
Test script to check Ollama connection and Llama 3 model availability
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

def test_ollama_connection():
    """Test if Ollama is running and Llama 3 model is available"""
    try:
        from langchain_ollama import OllamaLLM
        
        print("Testing Ollama connection...")
        
        # Initialize the LLM
        llm = OllamaLLM(
            model="llama3",
            temperature=0.7,
            base_url="http://localhost:11434"
        )
        
        # Test with a simple query
        print("Sending test message to Llama 3...")
        response = llm.invoke("Hello! Please respond with 'Ollama is working correctly' if you can understand this message.")
        
        print(f"✅ Success! Ollama response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {e}")
        print("\nTroubleshooting steps:")
        print("1. Make sure Ollama is installed and running")
        print("2. Check if Llama 3 model is installed: ollama list")
        print("3. If not installed, run: ollama pull llama3")
        print("4. Start Ollama service if not running")
        return False

def test_chatbot_service():
    """Test the chatbot service functionality"""
    try:
        from django.contrib.auth.models import User
        from analyzer.chatbot_service import ChatbotService, DataAnalysisService
        
        print("\nTesting chatbot service...")
        
        # Create or get a test user
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test data analysis service
        data_service = DataAnalysisService(user)
        overview = data_service.get_client_overview()
        print(f"✅ Data service working. Overview: {overview}")
        
        # Test chatbot service initialization
        chatbot = ChatbotService(user)
        print(f"✅ Chatbot service initialized. LLM available: {chatbot.llm is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chatbot service: {e}")
        return False

if __name__ == "__main__":
    print("🤖 Testing AI Chatbot Implementation")
    print("=" * 50)
    
    # Test Ollama connection
    ollama_ok = test_ollama_connection()
    
    # Test chatbot service
    service_ok = test_chatbot_service()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Ollama Connection: {'✅ PASS' if ollama_ok else '❌ FAIL'}")
    print(f"Chatbot Service: {'✅ PASS' if service_ok else '❌ FAIL'}")
    
    if ollama_ok and service_ok:
        print("\n🎉 All tests passed! The AI chatbot is ready to use.")
        print("\nNext steps:")
        print("1. Visit http://127.0.0.1:8000/ai-agent/ to test the chat interface")
        print("2. Upload and process some JIRA data files for the AI to analyze")
        print("3. Ask the AI questions about your client data and business insights")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
