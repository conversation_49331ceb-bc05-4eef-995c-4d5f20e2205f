{% extends 'base.html' %}

{% block title %}AI Agent - Vermeg Insights{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: 70vh;
        display: flex;
        flex-direction: column;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        background: #fff;
    }

    .chat-header {
        background: linear-gradient(135deg, #e31937, #c41230);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem 0.5rem 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
    }

    .message {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }

    .message.user {
        justify-content: flex-end;
    }

    .message.ai {
        justify-content: flex-start;
    }

    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        word-wrap: break-word;
    }

    .message.user .message-content {
        background: #e31937;
        color: white;
        border-bottom-right-radius: 0.25rem;
    }

    .message.ai .message-content {
        background: white;
        border: 1px solid #dee2e6;
        border-bottom-left-radius: 0.25rem;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 0 0.5rem;
    }

    .message.user .message-avatar {
        background: #e31937;
        color: white;
        order: 2;
    }

    .message.ai .message-avatar {
        background: #6c757d;
        color: white;
    }

    .chat-input {
        border-top: 1px solid #dee2e6;
        padding: 1rem;
        background: white;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .typing-indicator {
        display: none;
        padding: 0.5rem 1rem;
        font-style: italic;
        color: #6c757d;
    }

    .typing-dots {
        display: inline-block;
        animation: typing 1.4s infinite;
    }

    @keyframes typing {
        0%, 60%, 100% { opacity: 0; }
        30% { opacity: 1; }
    }

    .sidebar {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        height: 70vh;
        overflow-y: auto;
    }

    .sidebar-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: bold;
    }

    .session-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .session-item:hover {
        background: #f8f9fa;
    }

    .session-item.active {
        background: #e31937;
        color: white;
    }

    .session-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .session-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .session-item.active .session-meta {
        color: rgba(255, 255, 255, 0.8);
    }

    .data-summary {
        background: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .data-summary h6 {
        color: #004085;
        margin-bottom: 0.5rem;
    }

    .data-metric {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .btn-chat {
        background: #e31937;
        border-color: #e31937;
        color: white;
    }

    .btn-chat:hover {
        background: #c41230;
        border-color: #c41230;
        color: white;
    }

    .welcome-message {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .welcome-message .icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #e31937;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="page-header mb-4">
                <i class="fas fa-robot text-danger"></i> AI Data Analyst Assistant
            </h1>
        </div>
    </div>

    <div class="row">
        <!-- Chat Sessions Sidebar -->
        <div class="col-md-3">
            <div class="sidebar">
                <div class="sidebar-header">
                    <i class="fas fa-comments"></i> Chat Sessions
                    <button class="btn btn-sm btn-outline-primary float-end" onclick="startNewChat()">
                        <i class="fas fa-plus"></i> New
                    </button>
                </div>
                <div id="sessionsList">
                    <!-- Sessions will be loaded here -->
                </div>
            </div>

            <!-- Data Summary -->
            <div class="data-summary mt-3" id="dataSummary">
                <h6><i class="fas fa-chart-bar"></i> Available Data</h6>
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <small class="d-block mt-2">Loading data summary...</small>
                </div>
            </div>
        </div>

        <!-- Main Chat Interface -->
        <div class="col-md-9">
            <div class="chat-container">
                <div class="chat-header">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-brain"></i> AI Data Analyst
                        </h5>
                        <small>Powered by Llama 3 - Ask me about your client data and business insights</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-light" onclick="clearChat()" title="Clear Chat">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message" id="welcomeMessage">
                        <div class="icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h4>Welcome to your AI Data Analyst!</h4>
                        <p>I can help you analyze client data, identify trends, and provide business insights. Here are some things you can ask me:</p>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success"></i> Client performance analysis</li>
                                    <li><i class="fas fa-check text-success"></i> Sentiment trends and insights</li>
                                    <li><i class="fas fa-check text-success"></i> Resolution time analysis</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success"></i> Draft professional emails</li>
                                    <li><i class="fas fa-check text-success"></i> Business recommendations</li>
                                    <li><i class="fas fa-check text-success"></i> Data-driven insights</li>
                                </ul>
                            </div>
                        </div>
                        <p class="mt-3"><strong>Try asking:</strong> "Show me clients with negative sentiment" or "Draft an email for client outreach"</p>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <div class="message ai">
                        <div class="message-avatar">AI</div>
                        <div class="message-content">
                            AI is typing<span class="typing-dots">...</span>
                        </div>
                    </div>
                </div>

                <div class="chat-input">
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput"
                               placeholder="Ask me about your client data, trends, or request business insights..."
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-chat" type="button" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                    <small class="text-muted mt-1 d-block">
                        <i class="fas fa-info-circle"></i>
                        I have access to your client metrics, sentiment analysis, and business data.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSessionId = null;
let isTyping = false;

// Initialize the chat interface
document.addEventListener('DOMContentLoaded', function() {
    loadChatSessions();
    loadDataSummary();
});

// Load chat sessions from the server
async function loadChatSessions() {
    try {
        const response = await fetch('/api/chat/sessions/');
        const data = await response.json();

        if (data.success) {
            displaySessions(data.sessions);
        } else {
            console.error('Error loading sessions:', data.error);
        }
    } catch (error) {
        console.error('Error loading sessions:', error);
    }
}

// Display sessions in the sidebar
function displaySessions(sessions) {
    const sessionsList = document.getElementById('sessionsList');

    if (sessions.length === 0) {
        sessionsList.innerHTML = `
            <div class="text-center p-3 text-muted">
                <i class="fas fa-comments"></i><br>
                <small>No conversations yet.<br>Start a new chat!</small>
            </div>
        `;
        return;
    }

    sessionsList.innerHTML = sessions.map(session => `
        <div class="session-item" onclick="loadSession('${session.session_id}')" data-session-id="${session.session_id}">
            <div class="session-title">${session.title}</div>
            <div class="session-meta">
                ${session.message_count} messages • ${formatDate(session.updated_at)}
            </div>
        </div>
    `).join('');
}

// Load data summary
async function loadDataSummary() {
    try {
        const response = await fetch('/api/chat/data-summary/');
        const data = await response.json();

        if (data.success && data.data_summary.has_data) {
            displayDataSummary(data.data_summary);
        } else {
            displayNoDataSummary();
        }
    } catch (error) {
        console.error('Error loading data summary:', error);
        displayNoDataSummary();
    }
}

// Display data summary
function displayDataSummary(summary) {
    const overview = summary.overview;
    const performance = summary.performance;

    document.getElementById('dataSummary').innerHTML = `
        <h6><i class="fas fa-chart-bar"></i> Available Data</h6>
        <div class="data-metric">
            <span>Total Clients:</span>
            <strong>${overview.total_clients || 0}</strong>
        </div>
        <div class="data-metric">
            <span>Total Tickets:</span>
            <strong>${overview.total_tickets || 0}</strong>
        </div>
        <div class="data-metric">
            <span>Avg Resolution:</span>
            <strong>${performance.avg_resolution_time ? performance.avg_resolution_time.toFixed(1) + 'd' : 'N/A'}</strong>
        </div>
        <div class="data-metric">
            <span>Critical Clients:</span>
            <strong class="text-danger">${performance.critical_clients || 0}</strong>
        </div>
        <small class="text-muted mt-2 d-block">
            <i class="fas fa-info-circle"></i>
            Data updated from your latest analysis
        </small>
    `;
}

// Display no data message
function displayNoDataSummary() {
    document.getElementById('dataSummary').innerHTML = `
        <h6><i class="fas fa-chart-bar"></i> Available Data</h6>
        <div class="text-center text-muted">
            <i class="fas fa-exclamation-triangle"></i><br>
            <small>No analysis data available.<br>Upload and process files first.</small>
        </div>
    `;
}

// Start a new chat session
function startNewChat() {
    currentSessionId = null;
    clearChatMessages();
    showWelcomeMessage();

    // Remove active class from all sessions
    document.querySelectorAll('.session-item').forEach(item => {
        item.classList.remove('active');
    });
}

// Load a specific chat session
async function loadSession(sessionId) {
    try {
        const response = await fetch(`/api/chat/history/${sessionId}/`);
        const data = await response.json();

        if (data.success) {
            currentSessionId = sessionId;
            displayChatHistory(data.history);

            // Update active session in sidebar
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-session-id="${sessionId}"]`).classList.add('active');
        } else {
            console.error('Error loading session:', data.error);
        }
    } catch (error) {
        console.error('Error loading session:', error);
    }
}

// Display chat history
function displayChatHistory(history) {
    const chatMessages = document.getElementById('chatMessages');
    hideWelcomeMessage();

    chatMessages.innerHTML = history.map(message => {
        const messageClass = message.type === 'human' ? 'user' : 'ai';
        const avatar = message.type === 'human' ? 'U' : 'AI';

        return `
            <div class="message ${messageClass}">
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${formatMessage(message.content)}</div>
            </div>
        `;
    }).join('');

    scrollToBottom();
}

// Send a message
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message || isTyping) return;

    // Clear input and add user message to chat
    messageInput.value = '';
    hideWelcomeMessage();
    addMessageToChat('user', message);

    // Show typing indicator
    showTypingIndicator();

    try {
        const response = await fetch('/api/chat/message/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                message: message,
                session_id: currentSessionId
            })
        });

        const data = await response.json();
        hideTypingIndicator();

        if (data.success) {
            currentSessionId = data.session_id;
            addMessageToChat('ai', data.response);

            // Reload sessions to update the sidebar
            loadChatSessions();
        } else {
            addMessageToChat('ai', `Error: ${data.error}`);
        }
    } catch (error) {
        hideTypingIndicator();
        addMessageToChat('ai', `Error: ${error.message}`);
    }
}

// Add message to chat interface
function addMessageToChat(type, content) {
    const chatMessages = document.getElementById('chatMessages');
    const messageClass = type === 'user' ? 'user' : 'ai';
    const avatar = type === 'user' ? 'U' : 'AI';

    const messageElement = document.createElement('div');
    messageElement.className = `message ${messageClass}`;
    messageElement.innerHTML = `
        <div class="message-avatar">${avatar}</div>
        <div class="message-content">${formatMessage(content)}</div>
    `;

    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

// Format message content (convert line breaks, etc.)
function formatMessage(content) {
    return content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

// Show/hide typing indicator
function showTypingIndicator() {
    isTyping = true;
    document.getElementById('typingIndicator').style.display = 'block';
    scrollToBottom();
}

function hideTypingIndicator() {
    isTyping = false;
    document.getElementById('typingIndicator').style.display = 'none';
}

// Show/hide welcome message
function showWelcomeMessage() {
    document.getElementById('welcomeMessage').style.display = 'block';
}

function hideWelcomeMessage() {
    document.getElementById('welcomeMessage').style.display = 'none';
}

// Clear chat messages
function clearChatMessages() {
    document.getElementById('chatMessages').innerHTML = '';
}

// Clear current chat
function clearChat() {
    if (confirm('Are you sure you want to clear this chat?')) {
        startNewChat();
    }
}

// Handle Enter key press
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Scroll to bottom of chat
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return 'Today';
    } else if (diffDays === 2) {
        return 'Yesterday';
    } else if (diffDays <= 7) {
        return `${diffDays - 1} days ago`;
    } else {
        return date.toLocaleDateString();
    }
}

// Get CSRF token
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}
</script>
{% endblock %}